/**
 * Firebase configuration for the admin interface
 */

import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBvOO-2Zt8xJQJQJQJQJQJQJQJQJQJQJQJQ", // TODO: Replace with actual API key from Firebase console
  authDomain: "quoteai-firebase.firebaseapp.com",
  projectId: "quoteai-firebase",
  storageBucket: "quoteai-firebase.firebasestorage.app",
  messagingSenderId: "123456789012", // TODO: Replace with actual sender ID
  appId: "1:123456789012:web:abcdefghijklmnop" // TODO: Replace with actual app ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

export default app;
