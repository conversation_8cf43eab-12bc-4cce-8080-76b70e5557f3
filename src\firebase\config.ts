/**
 * Firebase configuration for the admin interface
 */

import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBvOO-admin.tsx:24 Warning: You are importing createRoot from "react-dom" which is not supported. You should instead import it from "react-dom/client".
admin.tsx:24 ⚛️ React root created, rendering AdminApp...
admin.tsx:28 🎉 AdminApp rendered successfully

 Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
chunk-ZYFC6VSF.mjs:193 No routes matched location "/admin.html" 
iframe.js:271 
 GET https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyBvOO-2Zt8xJQJQJQJQJQJQJQJQJQJQJQJQ&cb=1755326326914 400 (Bad Request)
iframe.js:310 {"error":{"code":400,"message":"API key not valid. Please pass a valid API key.","errors":[{"message":"API key not valid. Please pass a valid API key.","domain":"global","reason":"badRequest"}],"status":"INVALID_ARGUMENT","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"API_KEY_INVALID","domain":"googleapis.com","metadata":{"service":"identitytoolkit.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}]}}", // TODO: Replace with actual API key from Firebase console
  authDomain: "quoteai-firebase.firebaseapp.com",
  projectId: "quoteai-firebase",
  storageBucket: "quoteai-firebase.firebasestorage.app",
  messagingSenderId: "123456789012", // TODO: Replace with actual sender ID
  appId: "1:123456789012:web:abcdefghijklmnop" // TODO: Replace with actual app ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

export default app;
