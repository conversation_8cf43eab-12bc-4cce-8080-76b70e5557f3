/**
 * Main admin application with routing
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AdminLogin from './components/Admin/AdminLogin';
import CustomerOnboardingForm from './components/Admin/CustomerOnboardingForm';
import ProtectedRoute from './components/Admin/ProtectedRoute';
import { useAuth } from './contexts/AuthContext';
import './styles/admin.css';

const AdminRoutes: React.FC = () => {
  const { logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Routes>
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route 
        path="/admin/onboard" 
        element={
          <ProtectedRoute>
            <CustomerOnboardingForm onLogout={handleLogout} />
          </ProtectedRoute>
        } 
      />
      <Route path="/admin" element={<Navigate to="/admin/onboard" replace />} />
      <Route path="/" element={<Navigate to="/admin" replace />} />
    </Routes>
  );
};

const AdminApp: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <div className="admin-app">
          <AdminRoutes />
        </div>
      </Router>
    </AuthProvider>
  );
};

export default AdminApp;
